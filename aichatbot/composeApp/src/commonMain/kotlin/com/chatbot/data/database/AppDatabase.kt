package com.chatbot.data.database

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.chatbot.core.utils.Converters
import com.chatbot.data.database.dao.LlmModelDao
import com.chatbot.data.database.dao.LlmProviderDao
import com.chatbot.data.database.entities.LlmModel
import com.chatbot.data.database.entities.LlmProvider

/**
 * 应用主数据库
 *
 * 使用Room数据库框架，支持多平台
 */
@Database(
    entities = [
        LlmProvider::class,
        LlmModel::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {

    /**
     * LLM提供商数据访问对象
     */
    abstract fun llmProviderDao(): LlmProviderDao

    /**
     * LLM模型数据访问对象
     */
    abstract fun llmModelDao(): LlmModelDao
}
/**
 * 平台特定的数据库构建器
 *
 * 每个平台都需要提供具体的实现
 */
expect fun getDatabaseBuilder(): RoomDatabase.Builder<AppDatabase>