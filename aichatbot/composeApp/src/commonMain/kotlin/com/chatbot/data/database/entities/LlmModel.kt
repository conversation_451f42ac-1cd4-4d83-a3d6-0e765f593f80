package com.chatbot.data.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.chatbot.core.utils.TimeUtils
import com.chatbot.data.database.common.Status
import kotlinx.serialization.Serializable

/**
 * LLM模型实体类
 *
 * 对应数据库表：llm_models
 * 存储LLM模型的详细信息，包括能力、定价和Token限制
 */
@Entity(
    tableName = "llm_models",
    foreignKeys = [
        ForeignKey(
            entity = LlmProvider::class,
            parentColumns = ["id"],
            childColumns = ["provider_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["provider_id", "model_id"], unique = true),
        Index(value = ["provider_id", "status"]),
        Index(value = ["model_id"]),
        Index(value = ["status"]),
        Index(value = ["family"])
    ]
)
@Serializable
data class LlmModel(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    @ColumnInfo(name = "provider_id")
    val providerId: Long,

    @ColumnInfo(name = "model_id")
    val modelId: String,

    @ColumnInfo(name = "version")
    val version: String? = null,

    @ColumnInfo(name = "description")
    val description: String? = null,

    @ColumnInfo(name = "family")
    val family: String? = null,

    @ColumnInfo(name = "status")
    val status: Int = Status.ACTIVE,

    @ColumnInfo(name = "release_date")
    val releaseDate: String? = null, // ISO date format

    @ColumnInfo(name = "capabilities")
    val capabilities: String, // JSON array string

    // 定价信息（从 model_pricing 表合并过来）
    @ColumnInfo(name = "input_tokens")
    val inputTokens: Double? = null, // 输入Token价格（每1K tokens）

    @ColumnInfo(name = "output_tokens")
    val outputTokens: Double? = null, // 输出Token价格（每1K tokens）

    @ColumnInfo(name = "total_tokens")
    val totalTokens: Double? = null, // 总Token价格（每1K tokens）

    @ColumnInfo(name = "currency")
    val currency: String = Currency.USD.code,

    @ColumnInfo(name = "pricing_tier")
    val pricingTier: String? = null,

    // Token限制
    @ColumnInfo(name = "max_tokens")
    val maxTokens: Int? = null, // 最大Token数

    @ColumnInfo(name = "max_input_tokens")
    val maxInputTokens: Int? = null, // 最大输入Token数

    @ColumnInfo(name = "max_output_tokens")
    val maxOutputTokens: Int? = null, // 最大输出Token数

    // 支持的语言
    @ColumnInfo(name = "supported_languages")
    val supportedLanguages: String? = null, // JSON array string

    @ColumnInfo(name = "created_at")
    val createdAt: Long = TimeUtils.currentTimestamp(),

    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = TimeUtils.currentTimestamp()
) {

    val maxInputLength: Long
        get() = (maxInputTokens ?: maxTokens ?: 16_384).toLong()

    val maxOutputLength: Long
        get() = (maxOutputTokens ?: maxTokens ?: 16_384).toLong()

    /**
     * 检查模型是否处于活跃状态
     */
    val isActive: Boolean
        get() = Status.toBoolean(status)

    /**
     * 获取有效的输入token价格
     */
    val effectiveInputPrice: Double?
        get() = inputTokens ?: totalTokens

    /**
     * 获取有效的输出token价格
     */
    val effectiveOutputPrice: Double?
        get() = outputTokens ?: totalTokens

    /**
     * 检查是否有有效的定价信息
     */
    val hasValidPricing: Boolean
        get() = inputTokens != null || outputTokens != null || totalTokens != null

    /**
     * 获取定价层级的显示文本
     */
    val tierDisplayText: String
        get() = pricingTier?.let { PricingTier.fromString(it)?.displayName } ?: "标准"

    /**
     * 获取模型的完整显示名称
     */
    val displayName: String
        get() = if (version.isNullOrBlank()) {
            modelId
        } else {
            "$modelId v$version"
        }

    /**
     * 获取模型家族的显示名称
     */
    val familyDisplayName: String?
        get() = family?.let { ModelFamily.fromString(it)?.displayName }
}

/**
 * 模型家族枚举定义
 */
enum class ModelFamily(val value: String, val displayName: String) {
    GPT("gpt", "GPT"),
    CLAUDE("claude", "Claude"),
    GEMINI("gemini", "Gemini"),
    LLAMA("llama", "LLaMA"),
    MISTRAL("mistral", "Mistral");

    companion object {
        /**
         * 根据字符串值获取枚举
         */
        fun fromString(value: String): ModelFamily? {
            return entries.find { it.value == value }
        }

        /**
         * 获取所有支持的模型家族列表
         */
        val ALL_FAMILIES = entries.map { it.value }
    }
}

/**
 * 定价层级枚举定义
 */
enum class PricingTier(val value: String, val displayName: String) {
    FREE("free", "免费"),
    BASIC("basic", "基础"),
    PREMIUM("premium", "高级"),
    ENTERPRISE("enterprise", "企业");

    companion object {
        /**
         * 根据字符串值获取枚举
         */
        fun fromString(value: String): PricingTier? {
            return entries.find { it.value == value }
        }

        /**
         * 获取所有支持的定价层级列表
         */
        val ALL_TIERS = entries.map { it.value }
    }
}

/**
 * 货币枚举定义
 */
enum class Currency(val code: String) {
    USD("USD"),
    CNY("CNY"),
    EUR("EUR"),
    GBP("GBP"),
    JPY("JPY");

    companion object {
        /**
         * 根据货币代码获取枚举
         */
        fun fromCode(code: String): Currency? {
            return entries.find { it.code == code }
        }

        /**
         * 获取所有支持的货币代码列表
         */
        val ALL_CODES = entries.map { it.code }
    }
}

/**
 * 模型能力枚举定义
 */
enum class ModelCapability(val value: String) {
    TEXT_GENERATION("text_generation"),
    IMAGE_INPUT("image_input"),
    IMAGE_GENERATION("image_generation"),
    AUDIO_INPUT("audio_input"),
    AUDIO_GENERATION("audio_generation"),
    VIDEO_INPUT("video_input"),
    VIDEO_GENERATION("video_generation"),
    FUNCTION_CALLING("function_calling"),
    EMBEDDINGS("embeddings"),
    FINE_TUNING("fine_tuning"),
    STREAMING("streaming"),
    SYSTEM_MESSAGES("system_messages"),
    BATCHING("batching"),
    CODE_GENERATION("code_generation"),
    REASONING("reasoning"),
    MULTIMODAL("multimodal");

    companion object {
        /**
         * 根据字符串值获取枚举
         */
        fun fromString(value: String): ModelCapability? {
            return entries.find { it.value == value }
        }

        /**
         * 获取所有支持的能力列表
         */
        val ALL_CAPABILITIES = entries.map { it.value }

        /**
         * 检查能力是否有效
         */
        fun isValidCapability(capability: String): Boolean {
            return fromString(capability) != null
        }
    }
}