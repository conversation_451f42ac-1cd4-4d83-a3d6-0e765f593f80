package com.chatbot.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.unit.dp
import com.drna.shadcn.compose.component.Button
import com.drna.shadcn.compose.component.Dialog
import com.drna.shadcn.compose.component.DialogAction
import com.drna.shadcn.compose.component.DialogCancel
import com.drna.shadcn.compose.component.ButtonVariant

/**
 * 基于 shadcn Dialog 的可展开对话框
 * 在原有 Dialog 基础上添加折叠展开功能
 *
 * @param onDismissRequest 关闭对话框回调
 * @param open 是否显示对话框
 * @param modifier 修饰符
 * @param title 标题内容
 * @param description 描述内容
 * @param content 主要内容
 * @param expandableContent 可展开的附加内容
 * @param actions 操作按钮（取消、确认等）
 * @param expandButtonText 展开按钮文本
 * @param collapseButtonText 收起按钮文本
 * @param onExpandedChange 展开状态变化回调
 * @param maxExpandedHeight 展开内容最大高度
 */
@Composable
fun ExpandableDialog(
    onDismissRequest: () -> Unit,
    open: Boolean,
    modifier: Modifier = Modifier,
    title: @Composable () -> Unit,
    description: @Composable () -> Unit,
    content: @Composable () -> Unit = {},
    expandableContent: @Composable () -> Unit = {},
    actions: @Composable () -> Unit = {},
    expandButtonText: String = "更多设置",
    collapseButtonText: String = "收起设置",
    onExpandedChange: ((Boolean) -> Unit)? = null,
    maxExpandedHeight: Int = 300
) {
    var expanded by remember { mutableStateOf(false) }

    // 当展开状态变化时通知外部
    LaunchedEffect(expanded) {
        onExpandedChange?.invoke(expanded)
    }

    Dialog(
        onDismissRequest = onDismissRequest,
        open = open,
        modifier = modifier,
        title = title,
        description = description,
        footer = {
            ExpandableDialogFooter(
                expanded = expanded,
                onToggleExpanded = { expanded = !expanded },
                expandButtonText = expandButtonText,
                collapseButtonText = collapseButtonText,
                actions = actions,
                expandableContent = expandableContent,
                maxExpandedHeight = maxExpandedHeight,
            )
        }
    )
}

/**
 * 可展开对话框的底部组件
 */
@Composable
private fun ExpandableDialogFooter(
    expanded: Boolean,
    onToggleExpanded: () -> Unit,
    expandButtonText: String,
    collapseButtonText: String,
    actions: @Composable () -> Unit,
    expandableContent: @Composable () -> Unit,
    maxExpandedHeight: Int,
) {
    Column {
        // 标准操作按钮区域
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 展开/收起按钮
            if (expandableContent != {}) {
                Button(
                    onClick = onToggleExpanded,
                    variant = ButtonVariant.Outline,
                ) {
                    Row {
                        Text(
                            text = if (expanded) collapseButtonText else expandButtonText,
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.primary,
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowDown,
                            contentDescription = null,
                            modifier = Modifier
                                .size(16.dp)
                                .rotate(if (expanded) 180f else 0f),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }

                }
            } else {
                Spacer(modifier = Modifier)
            }

            // 主要操作按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                actions()
            }
        }

        // 可展开内容区域
        AnimatedVisibility(
            visible = expanded,
            enter = expandVertically(
                animationSpec = tween(300),
                expandFrom = Alignment.Top
            ),
            exit = shrinkVertically(
                animationSpec = tween(300),
                shrinkTowards = Alignment.Top
            )
        ) {
            Column {
                Spacer(modifier = Modifier.height(16.dp))

                HorizontalDivider(
                    color = MaterialTheme.colorScheme.outlineVariant,
                    thickness = 1.dp
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 可滚动的展开内容
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = maxExpandedHeight.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .verticalScroll(rememberScrollState())
                    ) {
                        expandableContent()
                    }
                }

            }
        }
    }
}

/**
 * 快速显示展开对话框的便捷函数
 */
@Composable
fun ShowExpandableDialog(
    open: Boolean,
    onDismissRequest: () -> Unit,
    title: String,
    description: String? = null,
    content: @Composable () -> Unit = {},
    expandableContent: @Composable () -> Unit = {},
    onConfirm: (() -> Unit)? = null,
    onCancel: (() -> Unit)? = null,
    confirmText: String = "确认",
    cancelText: String = "取消",
    expandButtonText: String = "更多设置",
    collapseButtonText: String = "收起设置",
    initialExpanded: Boolean = false,
    onExpandedChange: ((Boolean) -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    ExpandableDialog(
        onDismissRequest = onDismissRequest,
        open = open,
        modifier = modifier,
        title = { Text(title) },
        description = { description?.let { Text(it) } ?: Unit },
        content = content,
        expandableContent = expandableContent,
        actions = {
            onCancel?.let {
                DialogCancel(onClick = it) {
                    Text(cancelText)
                }
            }

            onConfirm?.let {
                DialogAction(onClick = it) {
                    Text(confirmText)
                }
            }
        },
        expandButtonText = expandButtonText,
        collapseButtonText = collapseButtonText,
        onExpandedChange = onExpandedChange
    )
}