package com.chatbot.data.services

import ai.koog.prompt.llm.LLMCapability
import ai.koog.prompt.llm.LLMProvider
import com.chatbot.data.database.entities.LlmModel
import com.chatbot.data.database.entities.LlmProvider
import com.chatbot.data.database.entities.ProviderType
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class LlmServiceTest {

    // Note: Tests for createLLMClient() are commented out as they require network access
    // and actual API configuration. These would be better suited for integration tests.

    @Test
    fun `toKoogLLModel should convert LlmModel to LLModel for OPENAI`() {
        val llmModel = LlmModel(
            id = 1L,
            providerId = 1L,
            modelId = "gpt-4",
            capabilities = "[\"text_generation\", \"function_calling\", \"streaming\"]",
            maxInputTokens = 8192,
            maxOutputTokens = 4096
        )

        val result = llmModel.toKoogLLModel(ProviderType.OPENAI)

        assertEquals(LLMProvider.OpenAI, result.provider)
        assertEquals("gpt-4", result.id)
        assertEquals(8192L, result.contextLength)
        assertEquals(4096L, result.maxOutputTokens)
        assertTrue(result.capabilities.contains(LLMCapability.Temperature))
        assertTrue(result.capabilities.contains(LLMCapability.Completion))
        assertTrue(result.capabilities.contains(LLMCapability.Tools))
        assertTrue(result.capabilities.contains(LLMCapability.ToolChoice))
        assertTrue(result.capabilities.contains(LLMCapability.Audio))
        assertTrue(result.capabilities.contains(LLMCapability.OpenAIEndpoint.Completions))
    }

    @Test
    fun `toKoogLLModel should convert LlmModel to LLModel for ANTHROPIC`() {
        val llmModel = LlmModel(
            id = 1L,
            providerId = 1L,
            modelId = "claude-3-opus",
            capabilities = "[\"text_generation\", \"function_calling\", \"streaming\"]",
            maxInputTokens = 200000,
            maxOutputTokens = 4096
        )

        val result = llmModel.toKoogLLModel(ProviderType.ANTHROPIC)

        assertEquals(LLMProvider.Anthropic, result.provider)
        assertEquals("claude-3-opus", result.id)
        assertEquals(200000L, result.contextLength)
        assertEquals(4096L, result.maxOutputTokens)
    }

    @Test
    fun `toKoogLLModel should convert LlmModel to LLModel for GEMINI`() {
        val llmModel = LlmModel(
            id = 1L,
            providerId = 1L,
            modelId = "gemini-pro",
            capabilities = "[\"text_generation\", \"function_calling\", \"streaming\"]",
            maxInputTokens = 32768,
            maxOutputTokens = 8192
        )

        val result = llmModel.toKoogLLModel(ProviderType.GEMINI)

        assertEquals(LLMProvider.Google, result.provider)
        assertEquals("gemini-pro", result.id)
        assertEquals(32768L, result.contextLength)
        assertEquals(8192L, result.maxOutputTokens)
    }

    @Test
    fun `toKoogLLModel should convert LlmModel to LLModel for OPENAI_COMPATIBLE`() {
        val llmModel = LlmModel(
            id = 1L,
            providerId = 1L,
            modelId = "custom-model",
            capabilities = "[\"text_generation\", \"streaming\"]",
            maxInputTokens = 4096,
            maxOutputTokens = 2048
        )

        val result = llmModel.toKoogLLModel(ProviderType.OPENAI_COMPATIBLE)

        assertEquals(LLMProvider.OpenAI, result.provider)
        assertEquals("custom-model", result.id)
        assertEquals(4096L, result.contextLength)
        assertEquals(2048L, result.maxOutputTokens)
    }
    @Test
    fun `testKoog Provider`() {
        val llmModel = LlmModel(
            id = 1L,
            providerId = 1L,
            modelId = "custom-model",
            capabilities = "[\"text_generation\", \"streaming\"]",
            maxInputTokens = 4096,
            maxOutputTokens = 2048
        )

        val result = llmModel.toKoogLLModel(ProviderType.OPENAI_COMPATIBLE)

        assertEquals(LLMProvider.OpenAI, result.provider)
        assertEquals("custom-model", result.id)
        assertEquals(4096L, result.contextLength)
        assertEquals(2048L, result.maxOutputTokens)
    }

}